# TVBOX - IPTV 串流播放平台

TVBOX 是一款針對 IPTV 使用者設計的現代化影音串流播放平台，支援 .m3u / .m3u8 等格式的直播清單。

## ✨ 功能特色

- 🎥 **多格式支援**: 支援 m3u、m3u8、HLS 等常見串流格式
- 📱 **響應式設計**: 適配桌面和行動裝置
- 🔍 **智慧搜尋**: 快速搜尋和過濾頻道
- ❤️ **收藏功能**: 收藏喜愛的頻道
- 📋 **播放清單管理**: 管理多個播放清單
- 📱 **QR 掃描**: 快速匯入播放清單
- 🎨 **現代化 UI**: 使用 TailwindCSS 設計
- 💾 **本地儲存**: 自動儲存設定和收藏

## 🚀 快速開始

### 安裝依賴
```bash
npm install
```

### 啟動開發伺服器
```bash
npm run dev
```

### 建構生產版本
```bash
npm run build
```

## 📖 使用說明

### 1. 載入播放清單
- 在首頁輸入 .m3u 或 .m3u8 播放清單的 URL
- 支援 GitHub raw 連結、直播源等
- 或使用 QR 掃描功能快速匯入

### 2. 瀏覽頻道
- 載入成功後會顯示所有可用頻道
- 支援按群組分類和搜尋功能
- 點擊頻道卡片即可開始播放

### 3. 播放控制
- 支援播放/暫停、音量調節
- 全螢幕播放模式
- 進度條控制

### 4. 收藏管理
- 點擊愛心圖示收藏頻道
- 在設定頁面查看收藏統計

## 🛠️ 技術棧

- **前端框架**: React 18 + TypeScript
- **建構工具**: Vite
- **UI 框架**: TailwindCSS + Headless UI
- **播放器**: HLS.js
- **狀態管理**: Zustand
- **路由**: React Router
- **QR 掃描**: html5-qrcode
- **圖示**: Heroicons

## 📁 專案結構

```
src/
├── components/          # 可重用組件
│   ├── ui/             # 基礎 UI 組件
│   ├── Player/         # 播放器組件
│   ├── ChannelList/    # 頻道列表組件
│   └── QRScanner/      # QR 掃描組件
├── pages/              # 頁面組件
│   ├── Home/           # 首頁
│   ├── ChannelList/    # 頻道列表頁
│   ├── Player/         # 播放頁面
│   └── Settings/       # 設定頁面
├── services/           # 業務邏輯服務
│   ├── m3uParser.ts    # M3U 解析服務
│   ├── storage.ts      # 本地儲存服務
│   └── api.ts          # API 服務
├── stores/             # 狀態管理
├── types/              # TypeScript 類型定義
└── utils/              # 工具函數
```

## 🧪 測試

專案包含一個測試用的 M3U 檔案：
- 訪問: `http://localhost:5173/sample.m3u`
- 包含 3 個測試頻道用於功能驗證

## 🔧 開發

### 新增功能
1. 在對應的 `components/` 或 `pages/` 目錄下建立組件
2. 更新路由配置（如需要）
3. 更新狀態管理（如需要）

### 樣式開發
- 使用 TailwindCSS 工具類
- 自定義樣式在 `src/index.css` 中定義
- 響應式設計優先

## 📱 未來計劃

- [ ] Android APK 版本
- [ ] 離線播放功能
- [ ] 多語言支援
- [ ] 主題切換
- [ ] 播放歷史記錄
- [ ] 頻道分享功能

## 📄 授權

MIT License
