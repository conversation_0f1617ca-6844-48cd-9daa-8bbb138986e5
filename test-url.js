// 簡單的測試腳本來驗證 URL 處理
const testUrl = 'https://files.catbox.moe/zyat7k.m3u';

console.log('測試 URL:', testUrl);

// 測試 URL 驗證
function validateUrl(url) {
  try {
    // 清理 URL，移除可能的空白字符
    const cleanUrl = url.trim();
    
    // 基本格式檢查
    if (!cleanUrl || (!cleanUrl.startsWith('http://') && !cleanUrl.startsWith('https://'))) {
      return false;
    }

    // 嘗試創建 URL 對象來驗證格式
    new URL(cleanUrl);
    return true;
  } catch (error) {
    // 如果 URL 構造失敗，嘗試更寬鬆的驗證
    try {
      const cleanUrl = url.trim();
      // 簡單的正則表達式驗證
      const urlPattern = /^https?:\/\/[^\s/$.?#].[^\s]*$/i;
      return urlPattern.test(cleanUrl);
    } catch {
      return false;
    }
  }
}

// 測試安全的 Base64 編碼
function safeBase64Encode(str) {
  try {
    // 使用 encodeURIComponent 和 btoa 來處理 Unicode 字符
    return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (match, p1) => {
      return String.fromCharCode(parseInt(p1, 16));
    }));
  } catch (error) {
    // 如果 btoa 失敗，使用簡單的哈希算法
    return simpleHash(str);
  }
}

function simpleHash(str) {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 轉換為 32 位整數
  }
  return Math.abs(hash).toString(36);
}

// 執行測試
console.log('URL 驗證結果:', validateUrl(testUrl));

// 測試包含特殊字符的字符串
const testStrings = [
  'https://files.catbox.moe/zyat7k.m3u',
  '測試頻道1',
  '•咪咕「IPV4」',
  '𝟙𝟚𝟛',
  'CCTV-1 综合'
];

testStrings.forEach(str => {
  try {
    const encoded = safeBase64Encode(str);
    console.log(`"${str}" -> "${encoded}"`);
  } catch (error) {
    console.error(`編碼失敗: "${str}"`, error.message);
  }
});

console.log('測試完成！');
