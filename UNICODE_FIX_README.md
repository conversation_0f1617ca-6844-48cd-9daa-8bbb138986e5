# Unicode 字符處理修復

## 問題描述

您遇到的 "The string contains invalid characters" 錯誤是由於 JavaScript 的 `btoa()` 函數無法處理包含 Unicode 字符（非 ASCII 字符）的字符串導致的。

## 問題原因

1. **btoa() 限制**：JavaScript 的 `btoa()` 函數只能處理 Latin-1 字符集（0-255），當遇到 Unicode 字符時會拋出錯誤
2. **M3U 檔案中的特殊字符**：您的 M3U 檔案包含中文字符、特殊符號（如 `•`、`𝟙𝟚𝟛`）等 Unicode 字符
3. **ID 生成失敗**：在生成頻道 ID 和播放清單 ID 時，`btoa()` 函數遇到這些字符就會失敗

## 修復方案

### 1. 安全的 Base64 編碼

我們實現了一個安全的 Base64 編碼函數，能夠處理 Unicode 字符：

```javascript
private static safeBase64Encode(str: string): string {
  try {
    // 使用 encodeURIComponent 和 btoa 來處理 Unicode 字符
    return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (match, p1) => {
      return String.fromCharCode(parseInt(p1, 16));
    }));
  } catch (error) {
    // 如果 btoa 失敗，使用簡單的哈希算法
    return this.simpleHash(str);
  }
}
```

### 2. 備用哈希算法

當 Base64 編碼仍然失敗時，我們使用一個簡單的哈希算法作為備用方案：

```javascript
private static simpleHash(str: string): string {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 轉換為 32 位整數
  }
  return Math.abs(hash).toString(36);
}
```

### 3. 改進的 URL 驗證

我們也改進了 URL 驗證邏輯，使其更加寬鬆和容錯：

```javascript
private static isValidUrl(url: string): boolean {
  try {
    const cleanUrl = url.trim();
    
    if (!cleanUrl || (!cleanUrl.startsWith('http://') && !cleanUrl.startsWith('https://'))) {
      return false;
    }

    new URL(cleanUrl);
    return true;
  } catch (error) {
    // 備用驗證方法
    try {
      const cleanUrl = url.trim();
      const urlPattern = /^https?:\/\/[^\s/$.?#].[^\s]*$/i;
      return urlPattern.test(cleanUrl);
    } catch {
      return false;
    }
  }
}
```

## 修復的檔案

1. **src/services/m3uParser.ts**
   - 新增 `safeBase64Encode()` 方法
   - 新增 `simpleHash()` 方法
   - 更新 `generateChannelId()` 和 `generatePlaylistId()` 方法
   - 改進 `isValidUrl()` 方法

2. **src/pages/Home/Home.tsx**
   - 改進 `validateUrl()` 函數
   - 在處理 URL 前先清理空白字符

## 測試結果

我們的修復已經通過測試，能夠正確處理：

- ✅ 標準 ASCII URL：`https://files.catbox.moe/zyat7k.m3u`
- ✅ 中文字符：`測試頻道1`
- ✅ 特殊符號：`•咪咕「IPV4」`
- ✅ Unicode 數字：`𝟙𝟚𝟛`
- ✅ 混合字符：`CCTV-1 综合`

## 使用方法

現在您可以安全地使用包含任何字符的 M3U URL：

1. **在首頁輸入 URL**：`https://files.catbox.moe/zyat7k.m3u`
2. **點擊「載入播放清單」**
3. **系統會自動處理所有 Unicode 字符**

## 額外改進

除了修復 Unicode 問題，我們還進行了以下改進：

1. **更好的錯誤處理**：提供更詳細的錯誤訊息
2. **URL 清理**：自動移除 URL 前後的空白字符
3. **容錯機制**：多層驗證確保系統穩定性
4. **效能優化**：避免不必要的字符串操作

## 注意事項

1. **網路連接**：確保您的網路可以訪問 M3U 檔案的 URL
2. **CORS 政策**：某些伺服器可能有跨域限制
3. **檔案格式**：確保 M3U 檔案格式正確

## 故障排除

如果仍然遇到問題：

1. **檢查 URL**：確保 URL 可以在瀏覽器中直接訪問
2. **檢查網路**：確保網路連接正常
3. **使用測試頁面**：訪問 `/test` 頁面進行詳細測試
4. **查看控制台**：打開瀏覽器開發者工具查看詳細錯誤訊息

現在您的 TVBOX 應用程式應該能夠完美處理包含任何字符的 M3U 播放清單了！
