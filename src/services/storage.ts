import type { M3UPlaylist, M3UChannel, AppSettings, StorageData } from '../types';

const STORAGE_KEYS = {
  RECENT_PLAYLISTS: 'tvbox_recent_playlists',
  FAVORITE_CHANNELS: 'tvbox_favorite_channels',
  SETTINGS: 'tvbox_settings',
} as const;

const DEFAULT_SETTINGS: AppSettings = {
  theme: 'auto',
  autoplay: true,
  volume: 0.8,
  quality: 'auto',
  language: 'zh-TW',
};

export class StorageService {
  /**
   * 獲取最近使用的播放清單
   */
  static getRecentPlaylists(): M3UPlaylist[] {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.RECENT_PLAYLISTS);
      if (!data) return [];

      const playlists = JSON.parse(data) as M3UPlaylist[];
      // 轉換日期字串回 Date 物件
      return playlists.map(playlist => ({
        ...playlist,
        lastUpdated: new Date(playlist.lastUpdated)
      }));
    } catch (error) {
      console.error('Error loading recent playlists:', error);
      return [];
    }
  }

  /**
   * 儲存播放清單到最近使用
   */
  static saveRecentPlaylist(playlist: M3UPlaylist): void {
    try {
      const recentPlaylists = this.getRecentPlaylists();

      // 移除重複的播放清單
      const filteredPlaylists = recentPlaylists.filter(p => p.id !== playlist.id);

      // 添加到開頭
      const updatedPlaylists = [playlist, ...filteredPlaylists];

      // 限制最多保存 10 個
      const limitedPlaylists = updatedPlaylists.slice(0, 10);

      localStorage.setItem(
        STORAGE_KEYS.RECENT_PLAYLISTS,
        JSON.stringify(limitedPlaylists)
      );
    } catch (error) {
      console.error('Error saving recent playlist:', error);
    }
  }

  /**
   * 移除最近使用的播放清單
   */
  static removeRecentPlaylist(playlistId: string): void {
    try {
      const recentPlaylists = this.getRecentPlaylists();
      const filteredPlaylists = recentPlaylists.filter(p => p.id !== playlistId);

      localStorage.setItem(
        STORAGE_KEYS.RECENT_PLAYLISTS,
        JSON.stringify(filteredPlaylists)
      );
    } catch (error) {
      console.error('Error removing recent playlist:', error);
    }
  }

  /**
   * 獲取收藏的頻道
   */
  static getFavoriteChannels(): M3UChannel[] {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.FAVORITE_CHANNELS);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Error loading favorite channels:', error);
      return [];
    }
  }

  /**
   * 添加頻道到收藏
   */
  static addFavoriteChannel(channel: M3UChannel): void {
    try {
      const favorites = this.getFavoriteChannels();

      // 檢查是否已經收藏
      if (favorites.some(fav => fav.id === channel.id)) {
        return;
      }

      const updatedFavorites = [...favorites, channel];
      localStorage.setItem(
        STORAGE_KEYS.FAVORITE_CHANNELS,
        JSON.stringify(updatedFavorites)
      );
    } catch (error) {
      console.error('Error adding favorite channel:', error);
    }
  }

  /**
   * 從收藏中移除頻道
   */
  static removeFavoriteChannel(channelId: string): void {
    try {
      const favorites = this.getFavoriteChannels();
      const filteredFavorites = favorites.filter(fav => fav.id !== channelId);

      localStorage.setItem(
        STORAGE_KEYS.FAVORITE_CHANNELS,
        JSON.stringify(filteredFavorites)
      );
    } catch (error) {
      console.error('Error removing favorite channel:', error);
    }
  }

  /**
   * 檢查頻道是否已收藏
   */
  static isFavoriteChannel(channelId: string): boolean {
    const favorites = this.getFavoriteChannels();
    return favorites.some(fav => fav.id === channelId);
  }

  /**
   * 獲取應用設定
   */
  static getSettings(): AppSettings {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.SETTINGS);
      if (!data) return DEFAULT_SETTINGS;

      const settings = JSON.parse(data) as AppSettings;
      // 合併預設設定，確保所有屬性都存在
      return { ...DEFAULT_SETTINGS, ...settings };
    } catch (error) {
      console.error('Error loading settings:', error);
      return DEFAULT_SETTINGS;
    }
  }

  /**
   * 儲存應用設定
   */
  static saveSettings(settings: Partial<AppSettings>): void {
    try {
      const currentSettings = this.getSettings();
      const updatedSettings = { ...currentSettings, ...settings };

      localStorage.setItem(
        STORAGE_KEYS.SETTINGS,
        JSON.stringify(updatedSettings)
      );
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  }

  /**
   * 清除所有儲存的資料
   */
  static clearAll(): void {
    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });
    } catch (error) {
      console.error('Error clearing storage:', error);
    }
  }

  /**
   * 獲取所有儲存的資料
   */
  static getAllData(): StorageData {
    return {
      recentPlaylists: this.getRecentPlaylists(),
      favoriteChannels: this.getFavoriteChannels(),
      settings: this.getSettings(),
    };
  }

  /**
   * 匯出資料為 JSON
   */
  static exportData(): string {
    const data = this.getAllData();
    return JSON.stringify(data, null, 2);
  }

  /**
   * 從 JSON 匯入資料
   */
  static importData(jsonData: string): boolean {
    try {
      const data = JSON.parse(jsonData) as StorageData;

      if (data.recentPlaylists) {
        localStorage.setItem(
          STORAGE_KEYS.RECENT_PLAYLISTS,
          JSON.stringify(data.recentPlaylists)
        );
      }

      if (data.favoriteChannels) {
        localStorage.setItem(
          STORAGE_KEYS.FAVORITE_CHANNELS,
          JSON.stringify(data.favoriteChannels)
        );
      }

      if (data.settings) {
        localStorage.setItem(
          STORAGE_KEYS.SETTINGS,
          JSON.stringify(data.settings)
        );
      }

      return true;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  }
}
