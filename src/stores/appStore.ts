import { create } from 'zustand';
import type { M3UPlaylist, M3UChannel, AppSettings } from '../types';
import { StorageService } from '../services/storage';
import { M3UParser } from '../services/m3uParser';

interface AppStore {
  // 狀態
  currentPlaylist: M3UPlaylist | null;
  recentPlaylists: M3UPlaylist[];
  favoriteChannels: M3UChannel[];
  settings: AppSettings;
  isLoading: boolean;
  error: string | null;

  // 播放清單相關動作
  loadPlaylistFromUrl: (url: string) => Promise<void>;
  setCurrentPlaylist: (playlist: M3UPlaylist | null) => void;
  removeRecentPlaylist: (playlistId: string) => void;

  // 收藏頻道相關動作
  addFavoriteChannel: (channel: M3UChannel) => void;
  removeFavoriteChannel: (channelId: string) => void;
  isFavoriteChannel: (channelId: string) => boolean;

  // 設定相關動作
  updateSettings: (settings: Partial<AppSettings>) => void;

  // 通用動作
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  initializeApp: () => void;
}

export const useAppStore = create<AppStore>((set, get) => ({
  // 初始狀態
  currentPlaylist: null,
  recentPlaylists: [],
  favoriteChannels: [],
  settings: StorageService.getSettings(),
  isLoading: false,
  error: null,

  // 播放清單相關動作
  loadPlaylistFromUrl: async (url: string) => {
    set({ isLoading: true, error: null });

    try {
      let result;

      // 檢查是否為 GitHub URL，使用專門的解析方法
      if (url.includes('github.com')) {
        result = await M3UParser.parseFromGitHubUrl(url);
      } else {
        result = await M3UParser.parseFromUrl(url);
      }

      if (result.success && result.data) {
        const playlist = result.data;

        // 儲存到最近使用
        StorageService.saveRecentPlaylist(playlist);

        // 更新狀態
        set({
          currentPlaylist: playlist,
          recentPlaylists: StorageService.getRecentPlaylists(),
          isLoading: false,
          error: null
        });
      } else {
        set({
          isLoading: false,
          error: result.error || '載入播放清單失敗'
        });
      }
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : '載入播放清單時發生未知錯誤'
      });
    }
  },

  setCurrentPlaylist: (playlist) => {
    set({ currentPlaylist: playlist });
  },

  removeRecentPlaylist: (playlistId) => {
    StorageService.removeRecentPlaylist(playlistId);
    set({ recentPlaylists: StorageService.getRecentPlaylists() });
  },

  // 收藏頻道相關動作
  addFavoriteChannel: (channel) => {
    StorageService.addFavoriteChannel(channel);
    set({ favoriteChannels: StorageService.getFavoriteChannels() });
  },

  removeFavoriteChannel: (channelId) => {
    StorageService.removeFavoriteChannel(channelId);
    set({ favoriteChannels: StorageService.getFavoriteChannels() });
  },

  isFavoriteChannel: (channelId) => {
    const { favoriteChannels } = get();
    return favoriteChannels.some(channel => channel.id === channelId);
  },

  // 設定相關動作
  updateSettings: (newSettings) => {
    const currentSettings = get().settings;
    const updatedSettings = { ...currentSettings, ...newSettings };

    StorageService.saveSettings(updatedSettings);
    set({ settings: updatedSettings });
  },

  // 通用動作
  setLoading: (loading) => {
    set({ isLoading: loading });
  },

  setError: (error) => {
    set({ error });
  },

  clearError: () => {
    set({ error: null });
  },

  initializeApp: () => {
    // 從本地儲存載入資料
    const recentPlaylists = StorageService.getRecentPlaylists();
    const favoriteChannels = StorageService.getFavoriteChannels();
    const settings = StorageService.getSettings();

    set({
      recentPlaylists,
      favoriteChannels,
      settings
    });
  }
}));
