import { create } from 'zustand';
import type { M3UChannel } from '../types';

interface PlayerStore {
  // 播放器狀態
  isPlaying: boolean;
  currentChannel: M3UChannel | null;
  volume: number;
  isMuted: boolean;
  isFullscreen: boolean;
  currentTime: number;
  duration: number;
  isLoading: boolean;
  error: string | null;

  // HLS 播放器實例
  hlsInstance: any;

  // 播放控制動作
  play: () => void;
  pause: () => void;
  togglePlay: () => void;
  setCurrentChannel: (channel: M3UChannel | null) => void;

  // 音量控制
  setVolume: (volume: number) => void;
  toggleMute: () => void;

  // 全螢幕控制
  toggleFullscreen: () => void;
  setFullscreen: (fullscreen: boolean) => void;

  // 時間控制
  setCurrentTime: (time: number) => void;
  setDuration: (duration: number) => void;
  seekTo: (time: number) => void;

  // 載入狀態
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;

  // HLS 實例管理
  setHlsInstance: (hls: any) => void;
  destroyHlsInstance: () => void;

  // 重置播放器
  reset: () => void;
}

export const usePlayerStore = create<PlayerStore>((set, get) => ({
  // 初始狀態
  isPlaying: false,
  currentChannel: null,
  volume: 0.8,
  isMuted: false,
  isFullscreen: false,
  currentTime: 0,
  duration: 0,
  isLoading: false,
  error: null,
  hlsInstance: null,

  // 播放控制動作
  play: () => {
    set({ isPlaying: true, error: null });
  },

  pause: () => {
    set({ isPlaying: false });
  },

  togglePlay: () => {
    const { isPlaying } = get();
    set({ isPlaying: !isPlaying });
  },

  setCurrentChannel: (channel) => {
    console.log('設置當前頻道:', channel?.name || 'null');
    set({
      currentChannel: channel,
      currentTime: 0,
      duration: 0,
      error: null,
      isLoading: false
    });
  },

  // 音量控制
  setVolume: (volume) => {
    // 確保音量在 0-1 範圍內
    const clampedVolume = Math.max(0, Math.min(1, volume));
    set({
      volume: clampedVolume,
      isMuted: clampedVolume === 0
    });
  },

  toggleMute: () => {
    const { isMuted, volume } = get();
    if (isMuted) {
      // 取消靜音，恢復之前的音量或設為 0.8
      set({
        isMuted: false,
        volume: volume > 0 ? volume : 0.8
      });
    } else {
      // 靜音
      set({ isMuted: true });
    }
  },

  // 全螢幕控制
  toggleFullscreen: () => {
    const { isFullscreen } = get();
    set({ isFullscreen: !isFullscreen });
  },

  setFullscreen: (fullscreen) => {
    set({ isFullscreen: fullscreen });
  },

  // 時間控制
  setCurrentTime: (time) => {
    set({ currentTime: time });
  },

  setDuration: (duration) => {
    set({ duration });
  },

  seekTo: (time) => {
    const { duration } = get();
    const clampedTime = Math.max(0, Math.min(duration, time));
    set({ currentTime: clampedTime });
  },

  // 載入狀態
  setLoading: (loading) => {
    set({ isLoading: loading });
  },

  setError: (error) => {
    set({
      error,
      isLoading: false,
      isPlaying: false
    });
  },

  clearError: () => {
    set({ error: null });
  },

  // HLS 實例管理
  setHlsInstance: (hls) => {
    // 先銷毀舊的實例
    const { hlsInstance } = get();
    if (hlsInstance && hlsInstance.destroy) {
      hlsInstance.destroy();
    }
    set({ hlsInstance: hls });
  },

  destroyHlsInstance: () => {
    const { hlsInstance } = get();
    if (hlsInstance && hlsInstance.destroy) {
      hlsInstance.destroy();
    }
    set({ hlsInstance: null });
  },

  // 重置播放器
  reset: () => {
    const { hlsInstance } = get();
    if (hlsInstance && hlsInstance.destroy) {
      hlsInstance.destroy();
    }

    set({
      isPlaying: false,
      currentChannel: null,
      currentTime: 0,
      duration: 0,
      isLoading: false,
      error: null,
      hlsInstance: null,
      isFullscreen: false
    });
  }
}));
