import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowLeftIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { useAppStore } from '../../stores/appStore';
import { usePlayerStore } from '../../stores/playerStore';
import { ChannelDiagnostic } from '../../components/ChannelDiagnostic/ChannelDiagnostic';
import { Button } from '../../components/ui/Button';
import type { M3UChannel } from '../../types';

export const DiagnosticPage: React.FC = () => {
  const navigate = useNavigate();
  const { currentPlaylist } = useAppStore();
  const { setCurrentChannel } = usePlayerStore();
  const [selectedChannels, setSelectedChannels] = useState<M3UChannel[]>([]);

  const handleChannelPlay = (channel: M3UChannel) => {
    setCurrentChannel(channel);
    navigate('/player');
  };

  const handleBackToChannels = () => {
    navigate('/channels');
  };

  const handleSelectRandomChannels = () => {
    if (!currentPlaylist?.channels.length) return;
    
    // 隨機選擇 5 個頻道進行診斷
    const shuffled = [...currentPlaylist.channels].sort(() => 0.5 - Math.random());
    setSelectedChannels(shuffled.slice(0, 5));
  };

  const handleSelectProblematicChannels = () => {
    if (!currentPlaylist?.channels.length) return;
    
    // 選擇可能有問題的頻道（基於 URL 模式）
    const problematic = currentPlaylist.channels.filter(channel => {
      const url = channel.url.toLowerCase();
      return (
        !url.includes('.m3u8') || // 非標準 HLS
        url.includes('localhost') || // 本地地址
        url.includes('192.168.') || // 內網地址
        url.includes('10.') || // 內網地址
        url.includes('172.') // 內網地址
      );
    });
    
    setSelectedChannels(problematic.slice(0, 10));
  };

  if (!currentPlaylist) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            沒有載入播放清單
          </h2>
          <p className="text-gray-600 mb-6">
            請先載入一個播放清單來進行診斷
          </p>
          <Button onClick={() => navigate('/')}>
            回到首頁
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 標題列 */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBackToChannels}
                leftIcon={<ArrowLeftIcon className="h-5 w-5" />}
              >
                返回
              </Button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">頻道診斷</h1>
                <p className="text-sm text-gray-500">
                  {currentPlaylist.name} • {currentPlaylist.totalChannels} 個頻道
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 說明區域 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-start space-x-3">
            <InformationCircleIcon className="h-6 w-6 text-blue-500 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="text-sm font-medium text-blue-900">關於頻道診斷</h3>
              <div className="mt-2 text-sm text-blue-700">
                <p className="mb-2">
                  頻道診斷工具可以幫助您識別為什麼某些頻道無法播放。常見問題包括：
                </p>
                <ul className="list-disc list-inside space-y-1">
                  <li><strong>CORS 限制：</strong>最常見的問題，伺服器不允許瀏覽器直接存取</li>
                  <li><strong>伺服器離線：</strong>串流伺服器暫時無法存取</li>
                  <li><strong>URL 格式錯誤：</strong>播放清單中的 URL 可能已經失效</li>
                  <li><strong>網路問題：</strong>連線速度慢或不穩定</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* 快速選擇 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">快速選擇</h2>
          <div className="flex flex-wrap gap-3">
            <Button
              variant="outline"
              onClick={handleSelectRandomChannels}
            >
              隨機選擇 5 個頻道
            </Button>
            <Button
              variant="outline"
              onClick={handleSelectProblematicChannels}
            >
              選擇可能有問題的頻道
            </Button>
            <Button
              variant="outline"
              onClick={() => setSelectedChannels([])}
            >
              清除選擇
            </Button>
          </div>
        </div>

        {/* 診斷結果 */}
        {selectedChannels.length > 0 ? (
          <div className="space-y-4">
            <h2 className="text-lg font-medium text-gray-900">
              診斷結果 ({selectedChannels.length} 個頻道)
            </h2>
            {selectedChannels.map((channel) => (
              <ChannelDiagnostic
                key={channel.id}
                channel={channel}
                onPlay={handleChannelPlay}
              />
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
            <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">尚未選擇頻道</h3>
            <p className="mt-1 text-sm text-gray-500">
              請使用上方的快速選擇按鈕，或手動選擇要診斷的頻道
            </p>
          </div>
        )}

        {/* 常見問題解決方案 */}
        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-yellow-900 mb-4">常見問題解決方案</h3>
          <div className="space-y-4 text-sm text-yellow-800">
            <div>
              <h4 className="font-medium">🚫 CORS 錯誤</h4>
              <p>這是最常見的問題。解決方案：</p>
              <ul className="list-disc list-inside ml-4 mt-1">
                <li>使用支援 CORS 的代理伺服器</li>
                <li>使用桌面應用程式而非瀏覽器</li>
                <li>聯繫串流提供者啟用 CORS</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium">🌐 網路錯誤</h4>
              <p>伺服器無法存取或回應慢：</p>
              <ul className="list-disc list-inside ml-4 mt-1">
                <li>檢查網路連線</li>
                <li>嘗試使用 VPN</li>
                <li>等待一段時間後重試</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium">📺 格式不支援</h4>
              <p>影片格式或編碼不相容：</p>
              <ul className="list-disc list-inside ml-4 mt-1">
                <li>確認是 HLS (.m3u8) 格式</li>
                <li>檢查瀏覽器相容性</li>
                <li>嘗試其他播放器</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
