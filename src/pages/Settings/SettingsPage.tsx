import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowLeftIcon,
  TrashIcon,
  ArrowDownTrayIcon,
  ArrowUpTrayIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import { useAppStore } from '../../stores/appStore';
import { StorageService } from '../../services/storage';
import { Button } from '../../components/ui/Button';
import { ErrorMessage } from '../../components/ui/ErrorMessage';

export const SettingsPage: React.FC = () => {
  const navigate = useNavigate();
  const { settings, updateSettings, favoriteChannels, recentPlaylists } = useAppStore();
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const handleSettingChange = (key: keyof typeof settings, value: any) => {
    updateSettings({ [key]: value });
    setMessage({ type: 'success', text: '設定已儲存' });
    setTimeout(() => setMessage(null), 3000);
  };

  const handleClearAllData = () => {
    if (window.confirm('確定要清除所有資料嗎？此操作無法復原。')) {
      StorageService.clearAll();
      window.location.reload();
    }
  };

  const handleExportData = () => {
    try {
      const data = StorageService.exportData();
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `tvbox-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      setMessage({ type: 'success', text: '資料已匯出' });
    } catch (error) {
      setMessage({ type: 'error', text: '匯出失敗' });
    }
  };

  const handleImportData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const success = StorageService.importData(content);
        if (success) {
          setMessage({ type: 'success', text: '資料已匯入，頁面將重新載入' });
          setTimeout(() => window.location.reload(), 2000);
        } else {
          setMessage({ type: 'error', text: '匯入失敗，檔案格式不正確' });
        }
      } catch (error) {
        setMessage({ type: 'error', text: '匯入失敗' });
      }
    };
    reader.readAsText(file);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* 頁面標題 */}
        <div className="mb-6">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              onClick={() => navigate('/')}
              leftIcon={<ArrowLeftIcon className="h-5 w-5" />}
            >
              返回
            </Button>
            
            <div className="flex items-center">
              <Cog6ToothIcon className="h-6 w-6 text-gray-600 mr-2" />
              <h1 className="text-2xl font-bold text-gray-900">設定</h1>
            </div>
          </div>
        </div>

        {/* 訊息提示 */}
        {message && (
          <div className="mb-6">
            <ErrorMessage
              message={message.text}
              variant={message.type === 'error' ? 'error' : 'info'}
              onDismiss={() => setMessage(null)}
            />
          </div>
        )}

        <div className="space-y-6">
          {/* 播放設定 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">播放設定</h2>
            
            <div className="space-y-4">
              {/* 自動播放 */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">自動播放</label>
                  <p className="text-sm text-gray-500">選擇頻道後自動開始播放</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.autoplay}
                    onChange={(e) => handleSettingChange('autoplay', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
              </div>

              {/* 預設音量 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  預設音量: {Math.round(settings.volume * 100)}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={settings.volume * 100}
                  onChange={(e) => handleSettingChange('volume', parseInt(e.target.value) / 100)}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                />
              </div>

              {/* 畫質偏好 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">畫質偏好</label>
                <select
                  value={settings.quality}
                  onChange={(e) => handleSettingChange('quality', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="auto">自動</option>
                  <option value="high">高畫質</option>
                  <option value="medium">中等畫質</option>
                  <option value="low">低畫質</option>
                </select>
              </div>
            </div>
          </div>

          {/* 介面設定 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">介面設定</h2>
            
            <div className="space-y-4">
              {/* 主題 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">主題</label>
                <select
                  value={settings.theme}
                  onChange={(e) => handleSettingChange('theme', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="auto">跟隨系統</option>
                  <option value="light">淺色</option>
                  <option value="dark">深色</option>
                </select>
              </div>

              {/* 語言 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">語言</label>
                <select
                  value={settings.language}
                  onChange={(e) => handleSettingChange('language', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="zh-TW">繁體中文</option>
                  <option value="zh-CN">简体中文</option>
                  <option value="en">English</option>
                </select>
              </div>
            </div>
          </div>

          {/* 資料統計 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">資料統計</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-primary-600">{recentPlaylists.length}</div>
                <div className="text-sm text-gray-600">最近播放清單</div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-primary-600">{favoriteChannels.length}</div>
                <div className="text-sm text-gray-600">收藏頻道</div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-primary-600">
                  {recentPlaylists.reduce((total, playlist) => total + playlist.totalChannels, 0)}
                </div>
                <div className="text-sm text-gray-600">總頻道數</div>
              </div>
            </div>
          </div>

          {/* 資料管理 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">資料管理</h2>
            
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  variant="outline"
                  onClick={handleExportData}
                  leftIcon={<ArrowDownTrayIcon className="h-5 w-5" />}
                >
                  匯出資料
                </Button>
                
                <div className="relative">
                  <input
                    type="file"
                    accept=".json"
                    onChange={handleImportData}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  />
                  <Button
                    variant="outline"
                    leftIcon={<ArrowUpTrayIcon className="h-5 w-5" />}
                  >
                    匯入資料
                  </Button>
                </div>
              </div>
              
              <div className="pt-4 border-t border-gray-200">
                <Button
                  variant="outline"
                  onClick={handleClearAllData}
                  leftIcon={<TrashIcon className="h-5 w-5" />}
                  className="text-red-600 border-red-300 hover:bg-red-50"
                >
                  清除所有資料
                </Button>
                <p className="text-sm text-gray-500 mt-2">
                  這將清除所有播放清單、收藏頻道和設定，此操作無法復原。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
