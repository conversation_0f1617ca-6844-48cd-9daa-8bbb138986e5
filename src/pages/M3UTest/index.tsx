import React, { useState } from 'react';
import { ArrowPathIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';
import { M3UParser } from '../../services/m3uParser';
import type { M3UPlaylist, M3UChannel } from '../../types';

export const M3UTestPage: React.FC = () => {
  const [url, setUrl] = useState('https://github.com/YanG-1989/m3u/blob/main/Gather.m3u');
  const [isLoading, setIsLoading] = useState(false);
  const [playlist, setPlaylist] = useState<M3UPlaylist | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [validationResults, setValidationResults] = useState<{
    valid: M3UChannel[];
    invalid: M3UChannel[];
    total: number;
  } | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  const handleParseUrl = async () => {
    if (!url.trim()) {
      setError('請輸入有效的 URL');
      return;
    }

    setIsLoading(true);
    setError(null);
    setPlaylist(null);
    setValidationResults(null);

    try {
      let result;
      
      // 檢查是否為 GitHub URL
      if (url.includes('github.com')) {
        result = await M3UParser.parseFromGitHubUrl(url);
      } else {
        result = await M3UParser.parseFromUrl(url);
      }

      if (result.success && result.data) {
        setPlaylist(result.data);
        setError(null);
      } else {
        setError(result.error || '解析失敗');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '發生未知錯誤');
    } finally {
      setIsLoading(false);
    }
  };

  const handleValidateChannels = async () => {
    if (!playlist || playlist.channels.length === 0) {
      setError('沒有頻道可以驗證');
      return;
    }

    setIsValidating(true);
    setValidationResults(null);

    try {
      // 只驗證前 20 個頻道以避免過多請求
      const channelsToValidate = playlist.channels.slice(0, 20);
      const results = await M3UParser.validateChannels(channelsToValidate, 3);
      setValidationResults(results);
    } catch (err) {
      setError(err instanceof Error ? err.message : '驗證頻道時發生錯誤');
    } finally {
      setIsValidating(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">M3U 解析器測試</h1>
          
          {/* URL 輸入區域 */}
          <div className="mb-6">
            <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-2">
              M3U URL
            </label>
            <div className="flex gap-3">
              <input
                type="url"
                id="url"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="輸入 M3U 檔案 URL..."
              />
              <button
                onClick={handleParseUrl}
                disabled={isLoading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {isLoading && <ArrowPathIcon className="w-4 h-4 animate-spin" />}
                解析
              </button>
            </div>
          </div>

          {/* 錯誤訊息 */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-center gap-2">
                <XCircleIcon className="w-5 h-5 text-red-500" />
                <span className="text-red-700">{error}</span>
              </div>
            </div>
          )}

          {/* 解析結果 */}
          {playlist && (
            <div className="space-y-6">
              {/* 播放清單資訊 */}
              <div className="bg-green-50 border border-green-200 rounded-md p-4">
                <div className="flex items-center gap-2 mb-3">
                  <CheckCircleIcon className="w-5 h-5 text-green-500" />
                  <span className="text-green-700 font-medium">解析成功！</span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">播放清單名稱：</span>
                    <span className="text-gray-700">{playlist.name}</span>
                  </div>
                  <div>
                    <span className="font-medium">頻道數量：</span>
                    <span className="text-gray-700">{playlist.totalChannels}</span>
                  </div>
                  <div>
                    <span className="font-medium">最後更新：</span>
                    <span className="text-gray-700">{playlist.lastUpdated.toLocaleString()}</span>
                  </div>
                  {playlist.tvgUrl && (
                    <div>
                      <span className="font-medium">EPG URL：</span>
                      <span className="text-gray-700 break-all">{playlist.tvgUrl}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* 頻道驗證 */}
              <div className="flex items-center gap-3">
                <button
                  onClick={handleValidateChannels}
                  disabled={isValidating}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  {isValidating && <ArrowPathIcon className="w-4 h-4 animate-spin" />}
                  驗證頻道（前 20 個）
                </button>
                <span className="text-sm text-gray-500">
                  驗證頻道 URL 是否可訪問
                </span>
              </div>

              {/* 驗證結果 */}
              {validationResults && (
                <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
                  <h3 className="font-medium text-gray-900 mb-3">驗證結果</h3>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-900">{validationResults.total}</div>
                      <div className="text-gray-500">總計</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{validationResults.valid.length}</div>
                      <div className="text-gray-500">有效</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">{validationResults.invalid.length}</div>
                      <div className="text-gray-500">無效</div>
                    </div>
                  </div>
                </div>
              )}

              {/* 頻道列表 */}
              <div>
                <h3 className="font-medium text-gray-900 mb-3">頻道列表（前 50 個）</h3>
                <div className="bg-gray-50 border border-gray-200 rounded-md max-h-96 overflow-y-auto">
                  <div className="divide-y divide-gray-200">
                    {playlist.channels.slice(0, 50).map((channel, index) => (
                      <div key={channel.id} className="p-3 hover:bg-gray-100">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className="text-xs text-gray-500">#{index + 1}</span>
                              <span className="font-medium text-gray-900 truncate">{channel.name}</span>
                              {channel.group && (
                                <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                  {channel.group}
                                </span>
                              )}
                            </div>
                            <div className="mt-1 text-xs text-gray-500 truncate">
                              {channel.url}
                            </div>
                            {channel.logo && (
                              <div className="mt-1">
                                <img 
                                  src={channel.logo} 
                                  alt={channel.name}
                                  className="w-8 h-8 object-cover rounded"
                                  onError={(e) => {
                                    (e.target as HTMLImageElement).style.display = 'none';
                                  }}
                                />
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                {playlist.channels.length > 50 && (
                  <div className="mt-2 text-sm text-gray-500 text-center">
                    顯示前 50 個頻道，共 {playlist.channels.length} 個頻道
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
