import React, { useEffect, useRef, useState } from 'react';
import { Html5QrcodeScanner } from 'html5-qrcode';
import { XMarkIcon, QrCodeIcon } from '@heroicons/react/24/outline';
import { Button } from '../ui/Button';

interface QRScannerProps {
  onScanSuccess: (decodedText: string) => void;
  onClose: () => void;
  isOpen: boolean;
}

export const QRScanner: React.FC<QRScannerProps> = ({
  onScanSuccess,
  onClose,
  isOpen
}) => {
  const scannerRef = useRef<Html5QrcodeScanner | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && !scannerRef.current) {
      initializeScanner();
    }

    return () => {
      if (scannerRef.current) {
        scannerRef.current.clear().catch(console.error);
        scannerRef.current = null;
      }
    };
  }, [isOpen]);

  const initializeScanner = () => {
    const config = {
      fps: 10,
      qrbox: { width: 250, height: 250 },
      aspectRatio: 1.0,
      disableFlip: false,
      supportedScanTypes: [],
    };

    const scanner = new Html5QrcodeScanner('qr-reader', config, false);

    scanner.render(
      (decodedText) => {
        // 掃描成功
        onScanSuccess(decodedText);
        setIsScanning(false);
        handleClose();
      },
      (error) => {
        // 掃描錯誤（通常是沒有找到 QR 碼，可以忽略）
        console.debug('QR scan error:', error);
      }
    );

    scannerRef.current = scanner;
    setIsScanning(true);
    setError(null);
  };

  const handleClose = () => {
    if (scannerRef.current) {
      scannerRef.current.clear().catch(console.error);
      scannerRef.current = null;
    }
    setIsScanning(false);
    setError(null);
    onClose();
  };

  const handleRetry = () => {
    setError(null);
    if (scannerRef.current) {
      scannerRef.current.clear().catch(console.error);
      scannerRef.current = null;
    }
    setTimeout(() => {
      initializeScanner();
    }, 100);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* 背景遮罩 */}
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={handleClose}
        />

        {/* 對話框 */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          {/* 標題列 */}
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <QrCodeIcon className="h-6 w-6 text-primary-600 mr-2" />
                <h3 className="text-lg font-medium text-gray-900">
                  掃描 QR 碼
                </h3>
              </div>
              <button
                onClick={handleClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* 掃描器容器 */}
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                請將 QR 碼對準攝影機進行掃描
              </p>

              {error ? (
                <div className="text-center py-8">
                  <div className="text-red-600 mb-4">
                    <p className="font-medium">掃描失敗</p>
                    <p className="text-sm">{error}</p>
                  </div>
                  <Button onClick={handleRetry} variant="primary">
                    重新嘗試
                  </Button>
                </div>
              ) : (
                <div id="qr-reader" className="w-full" />
              )}

              {isScanning && (
                <div className="text-center">
                  <div className="inline-flex items-center px-4 py-2 bg-blue-50 rounded-lg">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                    <span className="text-sm text-blue-600">正在掃描...</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 底部按鈕 */}
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <Button
              variant="secondary"
              onClick={handleClose}
              className="w-full sm:w-auto"
            >
              取消
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
