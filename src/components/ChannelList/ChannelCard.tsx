import React, { useState } from 'react';
import { HeartIcon, PlayIcon } from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import type { M3UChannel } from '../../types';
import { useAppStore } from '../../stores/appStore';
import { usePlayerStore } from '../../stores/playerStore';

interface ChannelCardProps {
  channel: M3UChannel;
  onPlay: (channel: M3UChannel) => void;
  className?: string;
}

export const ChannelCard: React.FC<ChannelCardProps> = ({
  channel,
  onPlay,
  className = ''
}) => {
  const { addFavoriteChannel, removeFavoriteChannel, isFavoriteChannel } = useAppStore();
  const { currentChannel } = usePlayerStore();
  const [isClicked, setIsClicked] = useState(false);

  const isFavorite = isFavoriteChannel(channel.id);
  const isCurrentChannel = currentChannel?.id === channel.id;

  const handleToggleFavorite = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isFavorite) {
      removeFavoriteChannel(channel.id);
    } else {
      addFavoriteChannel(channel);
    }
  };

  const handlePlay = () => {
    setIsClicked(true);
    onPlay(channel);

    // 重置點擊狀態
    setTimeout(() => {
      setIsClicked(false);
    }, 300);
  };

  return (
    <div
      className={`
        bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden
        hover:shadow-md transition-shadow duration-200 cursor-pointer
        ${isCurrentChannel ? 'ring-2 ring-primary-500 bg-primary-50' : ''}
        ${className}
      `}
      onClick={handlePlay}
    >
      {/* 頻道圖片 */}
      <div className="relative aspect-video bg-gray-100">
        {channel.logo ? (
          <img
            src={channel.logo}
            alt={channel.name}
            className="w-full h-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
            }}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
            <PlayIcon className="h-12 w-12 text-gray-400" />
          </div>
        )}

        {/* 播放覆蓋層 */}
        <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
          <PlayIcon className="h-12 w-12 text-white opacity-0 hover:opacity-100 transition-opacity duration-200" />
        </div>

        {/* 收藏按鈕 */}
        <button
          onClick={handleToggleFavorite}
          className="absolute top-2 right-2 p-1.5 bg-black bg-opacity-50 rounded-full hover:bg-opacity-70 transition-all duration-200"
        >
          {isFavorite ? (
            <HeartSolidIcon className="h-4 w-4 text-red-500" />
          ) : (
            <HeartIcon className="h-4 w-4 text-white" />
          )}
        </button>

        {/* 正在播放指示器 */}
        {isCurrentChannel && (
          <div className="absolute bottom-2 left-2 px-2 py-1 bg-primary-600 text-white text-xs rounded-full">
            正在播放
          </div>
        )}
      </div>

      {/* 頻道資訊 */}
      <div className="p-3">
        <h3 className="font-medium text-gray-900 truncate mb-1">
          {channel.name}
        </h3>

        <div className="flex items-center justify-between text-sm text-gray-500">
          <span className="truncate">
            {channel.group || '未分類'}
          </span>

          {channel.resolution && (
            <span className="px-2 py-1 bg-gray-100 rounded text-xs font-medium">
              {channel.resolution}
            </span>
          )}
        </div>

        {channel.language && (
          <div className="mt-2">
            <span className="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
              {channel.language}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};
