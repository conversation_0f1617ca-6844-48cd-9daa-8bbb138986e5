import React, { useState, useMemo } from 'react';
import { MagnifyingGlassIcon, FunnelIcon } from '@heroicons/react/24/outline';
import type { M3UChannel } from '../../types';
import { ChannelCard } from './ChannelCard';
import { Input } from '../ui/Input';
import { Button } from '../ui/Button';

interface ChannelListProps {
  channels: M3UChannel[];
  onChannelPlay: (channel: M3UChannel) => void;
  className?: string;
}

export const ChannelList: React.FC<ChannelListProps> = ({
  channels,
  onChannelPlay,
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGroup, setSelectedGroup] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'name' | 'group'>('name');

  // 獲取所有群組
  const groups = useMemo(() => {
    const groupSet = new Set(channels.map(channel => channel.group || '未分類'));
    return Array.from(groupSet).sort();
  }, [channels]);

  // 過濾和排序頻道
  const filteredAndSortedChannels = useMemo(() => {
    let filtered = channels;

    // 搜尋過濾
    if (searchTerm) {
      filtered = filtered.filter(channel =>
        channel.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (channel.group || '').toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // 群組過濾
    if (selectedGroup !== 'all') {
      filtered = filtered.filter(channel =>
        (channel.group || '未分類') === selectedGroup
      );
    }

    // 排序
    filtered.sort((a, b) => {
      if (sortBy === 'name') {
        return a.name.localeCompare(b.name);
      } else {
        const groupA = a.group || '未分類';
        const groupB = b.group || '未分類';
        if (groupA === groupB) {
          return a.name.localeCompare(b.name);
        }
        return groupA.localeCompare(groupB);
      }
    });

    return filtered;
  }, [channels, searchTerm, selectedGroup, sortBy]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 搜尋和過濾控制 */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* 搜尋框 */}
          <div className="flex-1">
            <Input
              placeholder="搜尋頻道名稱或群組..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftIcon={<MagnifyingGlassIcon className="h-5 w-5" />}
            />
          </div>

          {/* 群組過濾 */}
          <div className="sm:w-48">
            <select
              value={selectedGroup}
              onChange={(e) => setSelectedGroup(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="all">所有群組</option>
              {groups.map(group => (
                <option key={group} value={group}>
                  {group}
                </option>
              ))}
            </select>
          </div>

          {/* 排序選項 */}
          <div className="sm:w-32">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'name' | 'group')}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="name">按名稱</option>
              <option value="group">按群組</option>
            </select>
          </div>
        </div>

        {/* 結果統計 */}
        <div className="mt-3 text-sm text-gray-600">
          顯示 {filteredAndSortedChannels.length} / {channels.length} 個頻道
          {selectedGroup !== 'all' && ` (群組: ${selectedGroup})`}
        </div>
      </div>

      {/* 頻道網格 */}
      {filteredAndSortedChannels.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {filteredAndSortedChannels.map(channel => (
            <ChannelCard
              key={channel.id}
              channel={channel}
              onPlay={onChannelPlay}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <FunnelIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">沒有找到頻道</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm || selectedGroup !== 'all'
              ? '請嘗試調整搜尋條件或過濾器'
              : '播放清單中沒有可用的頻道'
            }
          </p>
          {(searchTerm || selectedGroup !== 'all') && (
            <Button
              variant="outline"
              size="sm"
              className="mt-4"
              onClick={() => {
                setSearchTerm('');
                setSelectedGroup('all');
              }}
            >
              清除過濾器
            </Button>
          )}
        </div>
      )}
    </div>
  );
};
