import React, { useState, useRef } from 'react';
import { PlayIcon, StopIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { Button } from '../ui/Button';
import type { M3UChannel } from '../../types';

interface PlaybackTestProps {
  channel: M3UChannel;
  className?: string;
}

export const PlaybackTest: React.FC<PlaybackTestProps> = ({
  channel,
  className = ''
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(message);
  };

  const handlePlay = async () => {
    const video = videoRef.current;
    if (!video) return;

    setError(null);
    setLogs([]);
    addLog('🎬 開始播放測試');
    addLog(`📺 頻道: ${channel.name}`);
    addLog(`🔗 URL: ${channel.url}`);

    try {
      // 方法 1: 直接設定 src
      addLog('🎯 方法 1: 直接設定 video.src');
      video.src = channel.url;
      
      addLog('⏳ 等待影片載入...');
      await video.load();
      
      addLog('▶️ 嘗試播放...');
      await video.play();
      
      setIsPlaying(true);
      addLog('✅ 播放成功！');
      
    } catch (error1) {
      addLog(`❌ 方法 1 失敗: ${error1}`);
      
      try {
        // 方法 2: 使用 source 元素
        addLog('🎯 方法 2: 使用 source 元素');
        video.innerHTML = `
          <source src="${channel.url}" type="application/x-mpegURL">
          <source src="${channel.url}" type="video/mp4">
          <source src="${channel.url}" type="video/webm">
        `;
        
        await video.load();
        await video.play();
        
        setIsPlaying(true);
        addLog('✅ 播放成功！');
        
      } catch (error2) {
        addLog(`❌ 方法 2 失敗: ${error2}`);
        
        try {
          // 方法 3: 強制設定 MIME 類型
          addLog('🎯 方法 3: 強制 HLS MIME 類型');
          const blob = await fetch(channel.url).then(r => r.blob());
          const url = URL.createObjectURL(new Blob([blob], { type: 'application/x-mpegURL' }));
          video.src = url;
          
          await video.load();
          await video.play();
          
          setIsPlaying(true);
          addLog('✅ 播放成功！');
          
        } catch (error3) {
          addLog(`❌ 方法 3 失敗: ${error3}`);
          setError('所有播放方法都失敗了');
        }
      }
    }
  };

  const handleStop = () => {
    const video = videoRef.current;
    if (video) {
      video.pause();
      video.currentTime = 0;
      setIsPlaying(false);
      addLog('⏹️ 停止播放');
    }
  };

  const handleVideoError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    const video = e.currentTarget;
    const error = video.error;
    
    if (error) {
      let errorMessage = '';
      switch (error.code) {
        case MediaError.MEDIA_ERR_ABORTED:
          errorMessage = 'MEDIA_ERR_ABORTED: 播放被中止';
          break;
        case MediaError.MEDIA_ERR_NETWORK:
          errorMessage = 'MEDIA_ERR_NETWORK: 網路錯誤';
          break;
        case MediaError.MEDIA_ERR_DECODE:
          errorMessage = 'MEDIA_ERR_DECODE: 解碼錯誤';
          break;
        case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
          errorMessage = 'MEDIA_ERR_SRC_NOT_SUPPORTED: 不支援的格式';
          break;
        default:
          errorMessage = `未知錯誤 (code: ${error.code})`;
      }
      
      addLog(`❌ 影片錯誤: ${errorMessage}`);
      setError(errorMessage);
    }
  };

  const handleVideoEvent = (eventName: string) => {
    return () => {
      addLog(`📡 影片事件: ${eventName}`);
    };
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">播放測試</h3>
        <div className="flex space-x-2">
          <Button
            size="sm"
            onClick={handlePlay}
            disabled={isPlaying}
            leftIcon={<PlayIcon className="h-4 w-4" />}
          >
            測試播放
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={handleStop}
            disabled={!isPlaying}
            leftIcon={<StopIcon className="h-4 w-4" />}
          >
            停止
          </Button>
        </div>
      </div>

      {/* 影片播放器 */}
      <div className="mb-4">
        <video
          ref={videoRef}
          className="w-full h-48 bg-black rounded"
          controls
          playsInline
          onError={handleVideoError}
          onLoadStart={handleVideoEvent('loadstart')}
          onLoadedMetadata={handleVideoEvent('loadedmetadata')}
          onCanPlay={handleVideoEvent('canplay')}
          onCanPlayThrough={handleVideoEvent('canplaythrough')}
          onPlay={handleVideoEvent('play')}
          onPause={handleVideoEvent('pause')}
          onWaiting={handleVideoEvent('waiting')}
          onProgress={handleVideoEvent('progress')}
        />
      </div>

      {/* 錯誤訊息 */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center space-x-2">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
            <span className="text-red-700 font-medium">播放失敗</span>
          </div>
          <p className="mt-1 text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* 日誌 */}
      <div className="bg-gray-50 rounded-md p-3">
        <h4 className="text-sm font-medium text-gray-900 mb-2">測試日誌</h4>
        <div className="text-xs text-gray-600 font-mono max-h-32 overflow-y-auto">
          {logs.length > 0 ? (
            logs.map((log, index) => (
              <div key={index} className="mb-1">{log}</div>
            ))
          ) : (
            <div className="text-gray-400">點擊「測試播放」開始測試...</div>
          )}
        </div>
      </div>
    </div>
  );
};
