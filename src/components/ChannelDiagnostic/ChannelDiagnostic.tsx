import React, { useState } from 'react';
import {
  PlayIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowPathIcon,
  InformationCircleIcon,
  BeakerIcon
} from '@heroicons/react/24/outline';
import { Button } from '../ui/Button';
import { PlaybackTest } from '../PlaybackTest/PlaybackTest';
import type { M3UChannel } from '../../types';

interface ChannelDiagnosticProps {
  channel: M3UChannel;
  onPlay: (channel: M3UChannel) => void;
  className?: string;
}

interface DiagnosticResult {
  test: string;
  status: 'success' | 'warning' | 'error' | 'pending';
  message: string;
  details?: string;
}

export const ChannelDiagnostic: React.FC<ChannelDiagnosticProps> = ({
  channel,
  onPlay,
  className = ''
}) => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<DiagnosticResult[]>([]);
  const [showDetails, setShowDetails] = useState(false);
  const [showPlaybackTest, setShowPlaybackTest] = useState(false);

  const runDiagnostic = async () => {
    setIsRunning(true);
    setResults([]);

    const tests: DiagnosticResult[] = [
      { test: 'URL 格式檢查', status: 'pending', message: '檢查中...' },
      { test: 'CORS 測試', status: 'pending', message: '檢查中...' },
      { test: 'HEAD 請求測試', status: 'pending', message: '檢查中...' },
      { test: 'HLS 格式檢查', status: 'pending', message: '檢查中...' },
      { test: '回應時間測試', status: 'pending', message: '檢查中...' }
    ];

    setResults([...tests]);

    // 1. URL 格式檢查
    try {
      new URL(channel.url);
      tests[0] = {
        test: 'URL 格式檢查',
        status: 'success',
        message: 'URL 格式正確',
        details: `協議: ${new URL(channel.url).protocol}`
      };
    } catch (error) {
      tests[0] = {
        test: 'URL 格式檢查',
        status: 'error',
        message: 'URL 格式無效',
        details: error instanceof Error ? error.message : '未知錯誤'
      };
    }
    setResults([...tests]);

    // 2. CORS 測試
    try {
      const startTime = Date.now();
      const response = await fetch(channel.url, {
        method: 'HEAD',
        mode: 'cors'
      });
      const responseTime = Date.now() - startTime;

      tests[1] = {
        test: 'CORS 測試',
        status: 'success',
        message: 'CORS 允許存取',
        details: `狀態碼: ${response.status}, 回應時間: ${responseTime}ms`
      };

      tests[4] = {
        test: '回應時間測試',
        status: responseTime < 3000 ? 'success' : 'warning',
        message: `回應時間: ${responseTime}ms`,
        details: responseTime < 3000 ? '回應速度良好' : '回應較慢，可能影響播放'
      };
    } catch (error) {
      tests[1] = {
        test: 'CORS 測試',
        status: 'error',
        message: 'CORS 限制',
        details: '伺服器不允許跨域存取，這是最常見的播放失敗原因'
      };

      tests[4] = {
        test: '回應時間測試',
        status: 'error',
        message: '無法測試回應時間',
        details: '由於 CORS 限制無法測試'
      };
    }
    setResults([...tests]);

    // 3. HEAD 請求測試 (no-cors 模式)
    try {
      await fetch(channel.url, {
        method: 'HEAD',
        mode: 'no-cors'
      });

      if (tests[1].status === 'error') {
        tests[2] = {
          test: 'HEAD 請求測試',
          status: 'warning',
          message: '伺服器回應 (no-cors)',
          details: '伺服器有回應，但有 CORS 限制'
        };
      } else {
        tests[2] = {
          test: 'HEAD 請求測試',
          status: 'success',
          message: '伺服器正常回應',
          details: '伺服器可以正常存取'
        };
      }
    } catch (error) {
      tests[2] = {
        test: 'HEAD 請求測試',
        status: 'error',
        message: '伺服器無回應',
        details: '伺服器可能離線或 URL 無效'
      };
    }
    setResults([...tests]);

    // 4. HLS 格式檢查
    const isHLS = channel.url.includes('.m3u8') ||
                  channel.url.includes('playlist.m3u8') ||
                  channel.url.includes('/hls/');

    tests[3] = {
      test: 'HLS 格式檢查',
      status: isHLS ? 'success' : 'warning',
      message: isHLS ? '檢測到 HLS 格式' : '非標準 HLS 格式',
      details: isHLS ? 'URL 包含 .m3u8，應該是 HLS 串流' : '可能是直播串流或其他格式'
    };

    setResults([...tests]);
    setIsRunning(false);
  };

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      case 'error':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'pending':
        return <ArrowPathIcon className="h-5 w-5 text-gray-400 animate-spin" />;
    }
  };

  const getStatusColor = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return 'text-green-700 bg-green-50 border-green-200';
      case 'warning':
        return 'text-yellow-700 bg-yellow-50 border-yellow-200';
      case 'error':
        return 'text-red-700 bg-red-50 border-red-200';
      case 'pending':
        return 'text-gray-700 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
      {/* 頻道資訊 */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">{channel.name}</h3>
          <p className="text-sm text-gray-500">{channel.group}</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowDetails(!showDetails)}
            leftIcon={<InformationCircleIcon className="h-4 w-4" />}
          >
            詳情
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={runDiagnostic}
            disabled={isRunning}
            leftIcon={isRunning ?
              <ArrowPathIcon className="h-4 w-4 animate-spin" /> :
              <ArrowPathIcon className="h-4 w-4" />
            }
          >
            診斷
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowPlaybackTest(!showPlaybackTest)}
            leftIcon={<BeakerIcon className="h-4 w-4" />}
          >
            播放測試
          </Button>
          <Button
            size="sm"
            onClick={() => onPlay(channel)}
            leftIcon={<PlayIcon className="h-4 w-4" />}
          >
            播放
          </Button>
        </div>
      </div>

      {/* 頻道詳情 */}
      {showDetails && (
        <div className="mb-4 p-3 bg-gray-50 rounded-md text-sm">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <div><strong>URL:</strong> <span className="break-all">{channel.url}</span></div>
            {channel.userAgent && <div><strong>User-Agent:</strong> {channel.userAgent}</div>}
            {channel.referer && <div><strong>Referer:</strong> {channel.referer}</div>}
            {channel.tvgId && <div><strong>TVG ID:</strong> {channel.tvgId}</div>}
          </div>
        </div>
      )}

      {/* 診斷結果 */}
      {results.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-900">診斷結果</h4>
          {results.map((result, index) => (
            <div
              key={index}
              className={`p-3 rounded-md border ${getStatusColor(result.status)}`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(result.status)}
                  <span className="font-medium">{result.test}</span>
                </div>
                <span className="text-sm">{result.message}</span>
              </div>
              {result.details && (
                <p className="mt-1 text-xs opacity-75">{result.details}</p>
              )}
            </div>
          ))}
        </div>
      )}

      {/* 播放測試 */}
      {showPlaybackTest && (
        <div className="mt-4">
          <PlaybackTest channel={channel} />
        </div>
      )}
    </div>
  );
};
