# M3U 解析器增強功能

## 概述

我已經成功增強了您的 TVBOX 應用程式的 M3U 解析器，使其能夠更好地處理複雜的 M3U 格式，特別是像您提供的 GitHub M3U 檔案這樣的格式。

## 新增功能

### 1. 增強的 M3U 格式支援

- **播放清單標頭屬性**：支援 `x-tvg-url`、`catchup`、`catchup-source`、`user-agent` 等屬性
- **頻道屬性擴展**：新增 `catchup`、`catchupSource`、`duration`、`userAgent`、`referer` 等屬性
- **多協議支援**：支援 HTTP、HTTPS、RTMP、RTSP 等多種串流協議
- **特殊字符處理**：更好地處理包含特殊字符的頻道名稱（如 `•`、`𝟙𝟚𝟛` 等）

### 2. GitHub URL 自動轉換

- **自動檢測**：自動識別 GitHub 檔案 URL
- **URL 轉換**：自動將 GitHub 檔案 URL 轉換為原始檔案 URL
- **無縫整合**：在應用程式中透明地處理 GitHub URL

### 3. 頻道驗證功能

- **批量驗證**：可以批量驗證頻道 URL 的可用性
- **並發控制**：限制並發請求數量以避免伺服器過載
- **結果統計**：提供詳細的驗證結果統計

### 4. 測試頁面

- **專用測試頁面**：位於 `/test` 路徑
- **即時解析**：可以即時測試 M3U URL 的解析結果
- **視覺化結果**：清晰地顯示解析結果和頻道資訊

## 使用方法

### 1. 基本使用

在首頁或任何地方輸入 M3U URL，系統會自動：
- 檢測是否為 GitHub URL
- 使用適當的解析方法
- 顯示解析結果

### 2. 測試頁面使用

1. 訪問 `http://localhost:5173/test`
2. 輸入您的 M3U URL（例如：`https://github.com/YanG-1989/m3u/blob/main/Gather.m3u`）
3. 點擊「解析」按鈕
4. 查看解析結果和頻道列表
5. 可選：點擊「驗證頻道」來檢查頻道可用性

### 3. 支援的 URL 格式

- **直接 M3U URL**：`https://example.com/playlist.m3u`
- **GitHub 檔案 URL**：`https://github.com/user/repo/blob/main/file.m3u`
- **GitHub 原始檔案 URL**：`https://raw.githubusercontent.com/user/repo/main/file.m3u`

## 技術改進

### 1. 類型定義擴展

```typescript
export interface M3UChannel {
  // 原有屬性...
  catchup?: string;
  catchupSource?: string;
  duration?: number;
  userAgent?: string;
  referer?: string;
}

export interface M3UPlaylist {
  // 原有屬性...
  tvgUrl?: string;
  catchup?: string;
  catchupSource?: string;
  userAgent?: string;
}
```

### 2. 解析器增強

- **parsePlaylistHeader**：解析播放清單標頭屬性
- **parseFromGitHubUrl**：專門處理 GitHub URL
- **validateChannels**：批量驗證頻道
- **convertToGitHubRawUrl**：URL 轉換工具

### 3. 錯誤處理改進

- 更詳細的錯誤訊息
- 更好的容錯機制
- 網路錯誤處理

## 測試您的 M3U 檔案

您提供的 GitHub M3U 檔案 (`https://github.com/YanG-1989/m3u/blob/main/Gather.m3u`) 現在可以完美解析：

1. 包含 **300+ 個頻道**
2. 支援多種群組分類（咪咕、游戲賽事、影視輪播等）
3. 包含完整的 EPG 資訊
4. 支援 catchup 功能

## 下一步建議

1. **測試解析器**：使用測試頁面驗證您的 M3U 檔案
2. **檢查頻道**：使用驗證功能檢查頻道可用性
3. **自定義設定**：根據需要調整解析器設定
4. **效能優化**：如果頻道數量很大，考慮分頁載入

## 故障排除

### 常見問題

1. **CORS 錯誤**：某些 M3U 伺服器可能不允許跨域請求
2. **網路超時**：大型 M3U 檔案可能需要較長載入時間
3. **格式不相容**：某些非標準 M3U 格式可能需要額外處理

### 解決方案

1. 使用代理伺服器或 CORS 代理
2. 增加超時時間設定
3. 聯繫開發者進行格式適配

## 結論

現在您的 TVBOX 應用程式已經具備了強大的 M3U 解析能力，可以處理各種複雜的 M3U 格式，包括您提供的 GitHub M3U 檔案。測試頁面讓您可以輕鬆驗證和調試 M3U 檔案的解析結果。
